use std::sync::Arc;

use crate::{
    automation::Automation,
    event_bus::{EventBus, events::DeviceEvent},
};

use auto_automations::Result;

#[derive(Debug)]
pub struct AutomationEngine {
    automations: Vec<Automation>,
    event_bus: Arc<EventBus>,
}

impl AutomationEngine {
    pub fn new(event_bus: Arc<EventBus>) -> Self {
        Self {
            automations: Vec::new(),
            event_bus,
        }
    }

    pub async fn start(&self) {
        self.event_bus
            .register_device_handler(
                "".to_string(),
                Arc::new(|event| self.subscribe_to_device_events(event)),
            )
            .await;
    }

    pub fn add_automation(&mut self, automation: Automation) -> &mut Self {
        self.automations.push(automation);

        self
    }

    pub fn remove_automation(&mut self, automation: Automation) -> &mut Self {
        self.automations.retain(|a| a.name != automation.name);

        self
    }

    async fn subscribe_to_device_events(&self, event: DeviceEvent) -> Result<()> {
        todo!();
    }
}
