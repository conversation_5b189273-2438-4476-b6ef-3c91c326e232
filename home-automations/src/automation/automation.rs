use crate::{automation::Workflow, values_objects::Id};

#[derive(Debug)]
pub struct Automation {
    pub id: Id,
    pub name: String,
    pub description: Option<String>,
    pub workflow: Workflow,
}

impl Automation {
    pub fn new(id: Id, name: String, description: Option<String>) -> Self {
        Self {
            id,
            name,
            description,
            workflow: Workflow::new(),
        }
    }
}